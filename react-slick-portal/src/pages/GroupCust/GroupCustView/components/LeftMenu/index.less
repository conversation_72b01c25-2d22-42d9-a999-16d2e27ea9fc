// ===== 样式变量定义 =====
@primary-color: #0085D0;
@primary-light: #E6F4FB;
@primary-gradient: linear-gradient(360deg, #0085D0 0%, #2DACF3 100%);

@text-primary: #333333;
@text-secondary: #666666;
@text-tertiary: #999999;
@text-white: #ffffff;

@bg-white: #ffffff;
@bg-light: #f0f0f0;
@bg-border: #f0f0f0;

@border-radius: 4px;
@box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
@box-shadow-menu: 2px 0px 4px 0px rgba(0, 0, 0, 0.08);

@menu-item-height: 40px;
@menu-item-padding: 9px 14px;
@menu-sub-item-padding: 9px 14px 9px 38px;

@tag-aclass-bg: #f0f9eb;
@tag-aclass-color: #67c23a;
@tag-industry-bg: #fffbe6;
@tag-industry-color: #e6a23c;
@tag-manager-bg: #fde2e2;
@tag-manager-color: #f56c6c;

// ===== 主容器样式 =====
.groupInfoContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: @bg-white;
  box-shadow: @box-shadow;

  // 搜索区域
  .searchWrapper {
    padding: 16px;
    background: @bg-white;
    border-bottom: 1px solid @bg-border;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  // 公司信息区域
  .companyInfo {
    padding: 16px 0;
    display: flex;
    align-items: start;

    .companyLogo {
      width: 56px;
      height: 56px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .companyName {
      font-size: 16px;
      height: 24px;
      line-height: 24px;
      color: @text-primary;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 192px;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .tags {
      .tag {
        display: inline-block;
        font-size: 12px;
        padding: 1px 4px;
        border-radius: 2px;
        margin: 0 4px 8px 0;
        white-space: nowrap;

        &.aClass {
          background-color: @tag-aclass-bg;
          color: @tag-aclass-color;
        }

        &.industry {
          background-color: @tag-industry-bg;
          color: @tag-industry-color;
        }

        &.manager {
          background-color: @tag-manager-bg;
          color: @tag-manager-color;
        }
      }
    }
  }

  // 信息行
  .infoRow {
    display: flex;
    align-items: baseline;
    font-size: 14px;

    span {
      font-weight: 400;
      color: @text-tertiary;
      margin-top: 8px;

      &:last-child {
        flex: 1;
        margin-bottom: 0;
        padding-left: 5px;
        white-space: pre-line;
        color: @text-primary;
      }
    }

    &:last-child {
      margin-bottom: 20px;
    }
  }

  // ===== 菜单样式 =====
  .antMenu {
    flex: 1;
    overflow-y: auto;
    background: transparent;
    border: none;

    :global {
      // 一级菜单项通用样式
      .ant-menu-submenu > .ant-menu-submenu-title,
      .ant-menu-item {
        height: @menu-item-height;
        background: @bg-white;
        margin: 0;
        padding: @menu-item-padding;
        display: flex;
        align-items: center;
        color: @text-primary;

        span {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
        }

        &:hover {
          background: @primary-light;
          color: @primary-color;
        }
      }

      // 二级菜单容器
      .ant-menu-sub {
        padding: 0 0 8px 0;
        background: transparent;
      }

      // 二级菜单项样式
      .ant-menu-submenu .ant-menu-item {
        height: @menu-item-height;
        padding: @menu-sub-item-padding;
        border-radius: @border-radius;
        color: @text-secondary;

        // 二级菜单项前的小圆点
        &::before {
          content: "";
          position: absolute;
          left: -12px;
          top: 50%;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #D9D9D9;
          transform: translateY(-50%);
        }

        span {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
        }

        &:hover {
          background: @primary-light;
          color: @primary-color;

          &::before {
            background: @primary-color;
          }

          span {
            color: @primary-color;
          }
        }
      }

      // ===== 选中状态样式 =====
      // 一级菜单项选中状态
      .ant-menu-item-selected,
      .ant-menu-submenu-selected > .ant-menu-submenu-title {
        background: @primary-gradient;
        box-shadow: @box-shadow-menu;
        color: @text-white !important;

        span {
          color: @text-white !important;
          font-weight: 500;
        }

        &:hover {
          background: @primary-gradient !important;
        }
      }

      // 特殊处理：一级菜单项的子菜单标题选中状态
      .ant-menu-item-selected > .ant-menu-submenu-title {
        background: @primary-light;

        span {
          color: @primary-color !important;
          font-weight: 500;
        }

        &:hover {
          background: @primary-gradient !important;
        }
      }

      // 二级菜单项选中状态
      .ant-menu-submenu .ant-menu-item-selected {
        background: @primary-light;

        span {
          color: @primary-color !important;
        }

        &:hover {
          background: @primary-light !important;
        }
      }

      // 子菜单箭头样式
      .ant-menu-submenu-arrow {
        color: @text-tertiary;
      }

      .ant-menu-submenu-selected .ant-menu-submenu-arrow {
        color: @text-white !important;
      }

      // 移除默认边框
      .ant-menu-item::after,
      .ant-menu-submenu::after {
        display: none;
      }
    }
  }
}

// ===== 搜索功能样式 =====
.searchInput {
  margin-bottom: 16px;

  :global {
    .ant-input {
      border-radius: @border-radius;
      height: @menu-item-height;
      font-size: 14px;
    }

    .ant-input-search-button {
      height: @menu-item-height;
      border-radius: 0 @border-radius @border-radius 0;
    }
  }
}

.searchResultList {
  max-height: 300px;
  overflow-y: auto;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
  }
}

.searchResultItem {
  padding: 10px 16px;
  cursor: pointer;
  border-radius: @border-radius;
  transition: all 0.3s ease;

  &:hover {
    background-color: @primary-light;
    color: @primary-color;
  }
}
